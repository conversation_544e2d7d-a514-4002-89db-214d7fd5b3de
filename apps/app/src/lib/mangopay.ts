import { env } from "@/lib/env"
import {
  CardType,
  MangopayCardRegistration,
  MangopayCardType,
  MangopayErrorResponse,
  MangopayRecurringPayIn,
  MangopayRecurringPayinRegistration,
  MangopayUser,
} from "@/types/mangopay"
import { logger } from "@coheadcoaching/lib"
import { PrismaClient } from "@prisma/client"

import { BrowserInfoType } from "./utils/browser-info"

const prisma = new PrismaClient()

export async function createMangopayUser(
  userId: string,
  userData: {
    firstName: string
    lastName: string
    address: {
      addressLine1: string
      addressLine2?: string | null
      city: string
      region?: string
      postalCode: string
      country: string
    }
    birthday?: number // timestamp
    email: string
    nationality?: string
    countryOfResidence?: string
  },
  category: "OWNER" | "PAYER" = "PAYER"
) {
  logger.log("Starting createMangopayUser function", { userId, userData, category })
  const user = await prisma.user.findUnique({ where: { id: userId } })
  if (!user) {
    logger.error("User not found", { userId })
    throw new Error("User not found")
  }

  const myHeaders = new Headers()
  myHeaders.append("Content-Type", "application/json")
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)

  const mangopayUserData = JSON.stringify({
    Address: {
      AddressLine1: userData.address.addressLine1,
      AddressLine2: userData.address.addressLine2,
      City: userData.address.city,
      Region: userData.address.region?.slice(0, 255),
      PostalCode: userData.address.postalCode,
      Country: userData.address.country,
    },
    FirstName: userData.firstName,
    LastName: userData.lastName,
    Birthday: userData.birthday,
    Nationality: userData.nationality,
    CountryOfResidence: userData.countryOfResidence,
    Tag: `User ${userId}`,
    Email: userData.email,
    TermsAndConditionsAccepted: true,
    UserCategory: category,
    PersonType: "NATURAL",
  })

  const createMangoUserOptions = {
    method: "POST",
    headers: myHeaders,
    body: mangopayUserData,
  }

  logger.log("Sending request to create Mangopay user", {
    url: `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/users`,
    options: createMangoUserOptions,
  })

  const response = await fetch(`${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/users`, createMangoUserOptions)

  if (!response.ok) {
    logger.error("Failed to create Mangopay user", { status: response.status, statusText: response.statusText })
    throw new Error("Impossible de créer l'utilisateur de paiement")
  }

  const mangopayUser = handleMangopayResponse<MangopayUser>(await response.json())
  logger.log("Mangopay user created successfully", { mangopayUser })

  // Mettre à jour l'utilisateur avec l'ID MangoPay
  await prisma.user.update({
    where: { id: userId },
    data: { mangopayUserId: mangopayUser.Id },
  })
  logger.log("User updated with Mangopay ID", { userId, mangopayUserId: mangopayUser.Id })

  return mangopayUser
}

export async function registerCard(
  userId: string,
  cardData: {
    cardNumber: string
    cardExpirationDate: string // Format: MMYY
    cardCvx: string
    cardType: CardType
  }
) {
  logger.log("Starting registerCard function", { userId })
  const user = await prisma.user.findUnique({ where: { id: userId } })
  if (!user || !user.mangopayUserId) {
    logger.error("User not found or no MangoPay account", { userId, mangopayUserId: user?.mangopayUserId })
    throw new Error("User not found or no MangoPay account")
  }

  // Créer d'abord une entrée temporaire dans notre base de données
  const userHavePrincipalCard = await prisma.mangopayCard.findFirst({
    where: { userId, isDefault: true, isTemp: false, isActive: true },
  })
  const tempCard = await prisma.mangopayCard.create({
    data: {
      userId,
      isTemp: true,
      isDefault: !userHavePrincipalCard,
    },
  })
  logger.log("Temporary card created", { tempCardId: tempCard.id })

  const myHeaders = new Headers()
  myHeaders.append("Content-Type", "application/json")
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)

  const registerCardData = JSON.stringify({
    UserId: user.mangopayUserId,
    Tag: `Card for user ${userId}`,
    Currency: "EUR",
    CardType: cardData.cardType,
  })

  const registerCardOptions = {
    method: "POST",
    headers: myHeaders,
    body: registerCardData,
  }

  logger.log("STEP 1 CARD", registerCardOptions)

  const response = await fetch(`${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/cardRegistrations`, registerCardOptions)

  if (!response.ok) {
    // Supprimer la carte temporaire en cas d'échec
    await prisma.mangopayCard.delete({ where: { id: tempCard.id } })
    logger.error("Failed to register card", { status: response.status, statusText: response.statusText })
    throw new Error("Impossible d'enregistrer votre carte")
  }

  const cardRegistration = handleMangopayResponse<MangopayCardRegistration>(await response.json())
  logger.log("Card registration response", { cardRegistration })

  if (cardRegistration.Status !== "CREATED") {
    await prisma.mangopayCard.delete({ where: { id: tempCard.id } })
    logger.error("Failed to register card", {
      status: cardRegistration.Status,
      resultCode: cardRegistration.ResultCode,
      resultMessage: cardRegistration.ResultMessage,
    })
    throw new Error(
      `Failed to register card! Code: ${cardRegistration.ResultCode}; Message ${cardRegistration.ResultMessage}`
    )
  }

  const tokenizeCardHeaders = new Headers()
  tokenizeCardHeaders.append("Content-Type", "application/x-www-form-urlencoded")

  const urlencoded = new URLSearchParams()
  urlencoded.append("data", cardRegistration.PreregistrationData)
  urlencoded.append("accessKeyRef", cardRegistration.AccessKey)
  urlencoded.append("cardNumber", cardData.cardNumber)
  urlencoded.append("cardExpirationDate", cardData.cardExpirationDate)
  urlencoded.append("cardCvx", cardData.cardCvx)

  const tokenizeCardOptions = {
    method: "POST",
    headers: tokenizeCardHeaders,
    body: urlencoded,
  }

  logger.log("STEP 2 CARD:", urlencoded)

  const tokenizeCardResponse = await fetch(cardRegistration.CardRegistrationURL, tokenizeCardOptions)

  if (!tokenizeCardResponse.ok) {
    await prisma.mangopayCard.delete({ where: { id: tempCard.id } })
    logger.error("Failed to register card on Tokenizing", {
      status: tokenizeCardResponse.status,
      statusText: tokenizeCardResponse.statusText,
    })
    throw new Error(
      `Failed to register card on Tokenizing: ${tokenizeCardResponse.status} ${tokenizeCardResponse.statusText}`
    )
  }

  const cardToken = await tokenizeCardResponse.text()

  logger.log("Card token received", { cardToken: cardToken })
  if (cardToken.startsWith("error")) {
    logger.error("Failed to tokenize the card", { cardToken })
    throw new Error("Failed to tokenize the card")
  }

  const updateCardRegistrationData = JSON.stringify({
    RegistrationData: cardToken,
    Id: cardRegistration.Id,
  })

  const updateCardRegOptions = {
    method: "PUT",
    headers: myHeaders,
    body: updateCardRegistrationData,
  }

  logger.log("STEP 3 CARD:", updateCardRegistrationData)

  const updateCardRegResponse = await fetch(
    `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/cardRegistrations/${cardRegistration.Id}`,
    updateCardRegOptions
  )

  if (!updateCardRegResponse.ok) {
    await prisma.mangopayCard.delete({ where: { id: tempCard.id } })
    logger.error("Failed to register card on update CardReg", { status: updateCardRegResponse.status })
    throw new Error("Impossible de finaliser l'enregistrement de votre carte")
  }

  const updateCardFinal = handleMangopayResponse<MangopayCardRegistration>(await updateCardRegResponse.json())
  logger.log("Final card registration update", { updateCardFinal })

  if (updateCardFinal.Status !== "VALIDATED") {
    await prisma.mangopayCard.delete({ where: { id: tempCard.id } })
    logger.error("Failed to register card on updateCardReg", {
      status: updateCardFinal.Status,
      resultCode: updateCardFinal.ResultCode,
      resultMessage: updateCardFinal.ResultMessage,
    })
    throw new Error(
      `Failed to register card on updateCardReg! Code: ${updateCardFinal.ResultCode}; Message ${updateCardFinal.ResultMessage}`
    )
  }

  // Mettre à jour la carte avec les informations de MangoPay
  const finalCard = await prisma.mangopayCard.update({
    where: { id: tempCard.id },
    data: {
      mangopayCardId: updateCardFinal.CardId,
      last4: cardData.cardNumber.slice(-4),
      expirationDate: cardData.cardExpirationDate,
      isTemp: false,
    },
  })
  logger.log("Card updated in database", { cardId: tempCard.id, mangopayCardId: updateCardFinal.CardId })

  return { cardRegistration: updateCardFinal, cardLocal: finalCard }
}

export async function getMangoPayUser(mangopayUserId: string) {
  const myHeaders = new Headers()
  myHeaders.append("Content-Type", "application/json")
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)

  const getMangoPayUserOptions = {
    method: "GET",
    headers: myHeaders,
  }

  logger.log("Sending request to get Mangopay user", {
    url: `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/users/${mangopayUserId}`,
    options: getMangoPayUserOptions,
  })

  const response = await fetch(
    `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/users/${mangopayUserId}`,
    getMangoPayUserOptions
  )

  if (!response.ok) {
    logger.error("Failed to get Mangopay user", { status: response.status, statusText: response.statusText })
    throw new Error("Impossible de récupérer les informations de paiement de l'utilisateur")
  }

  const mangopayUser = handleMangopayResponse<MangopayUser>(await response.json())
  logger.log("Mangopay user retrieved successfully", { mangopayUser })

  return mangopayUser
}

export function isMangopayError(data: unknown): data is MangopayErrorResponse {
  return (
    typeof data === "object" &&
    data !== null &&
    "Type" in data &&
    typeof (data as MangopayErrorResponse).Type === "string" &&
    "errors" in data
  )
}

export function handleMangopayResponse<T>(data: unknown): T {
  if (isMangopayError(data)) {
    logger.error("MangoPay error response", { errorResponse: data })

    let errorMessage = data.Message || "Une erreur s'est produite avec MangoPay"
    if (data.errors) {
      errorMessage += `: ${Object.keys(data.errors)
        .map((key) => `${key}: ${data.errors?.[key]}`)
        .join("; ")}`
    }

    throw new Error(errorMessage)
  }

  return data as T
}

interface CreateRecurringPaymentParams {
  authorId: string
  creditedUserId: string
  creditedWalletId: string
  cardId: string
  firstTransactionAmount: number
  nextTransactionAmount: number
  currency: string
  frequency: "Monthly" | "Annual"
  tag: string
  billing?: {
    address: {
      AddressLine1: string
      AddressLine2?: string
      City: string
      PostalCode: string
      Country: string
      Region?: string
    }
    firstName: string
    lastName: string
  }
}

export async function createRecurringPayment(params: CreateRecurringPaymentParams) {
  const myHeaders = new Headers()
  myHeaders.append("Content-Type", "application/json")
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)
  // Add Idempotency Key for safety
  myHeaders.append("Idempotency-Key", `recurring_payment_${Date.now()}`)

  const registrationPayload = JSON.stringify({
    AuthorId: params.authorId,
    CreditedUserId: params.creditedUserId,
    CreditedWalletId: params.creditedWalletId,
    CardId: params.cardId,
    // Define the FIRST payment
    FirstTransactionDebitedFunds: {
      Currency: params.currency,
      Amount: params.firstTransactionAmount,
    },
    FirstTransactionFees: { Currency: params.currency, Amount: 0 },
    // Define SUBSEQUENT payments
    NextTransactionDebitedFunds: {
      Currency: params.currency,
      Amount: params.nextTransactionAmount,
    },
    NextTransactionFees: { Currency: params.currency, Amount: 0 },
    Frequency: params.frequency,
    RecurringType: "SUBSCRIPTION",
    Tag: params.tag,
    Billing: params.billing,
  })

  const options = {
    method: "POST",
    headers: myHeaders,
    body: registrationPayload,
  }

  logger.log("Creating recurring payment registration", {
    url: `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/payins/createRecurringPayment`,
  })

  const response = await fetch(`${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/payins/createRecurringPayment`, options)

  if (!response.ok) {
    logger.error("Failed to create recurring payment registration", {
      status: response.status,
      statusText: response.statusText,
    })
    throw new Error("Impossible de créer l'enregistrement de paiement récurrent")
  }

  const result = handleMangopayResponse<MangopayRecurringPayinRegistration>(await response.json())
  logger.log("Recurring payment registration created successfully", { result })

  return result
}

interface InitiateRecurringPaymentParams {
  recurringPayinRegistrationId: string
  tag: string
  amount: number
  currency: string
  secureModeReturnURL: string
  statementDescriptor: string
  browserInfo: BrowserInfoType
  ipAddress?: string
}

export async function initiateRecurringPayment(params: InitiateRecurringPaymentParams) {
  const myHeaders = new Headers()
  myHeaders.append("Content-Type", "application/json")
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)
  // myHeaders.append("Idempotency-Key", `payment_init_${Date.now()}`)

  const paymentPayload = JSON.stringify({
    Tag: params.tag,
    DebitedFunds: {
      Currency: params.currency,
      Amount: params.amount,
    },
    Fees: {
      Currency: params.currency,
      Amount: 0,
    },
    SecureModeReturnURL: params.secureModeReturnURL,
    StatementDescriptor: params.statementDescriptor,
    BrowserInfo: params.browserInfo,
    IpAddress: params.ipAddress || "0.0.0.0",
    RecurringPayinRegistrationId: params.recurringPayinRegistrationId,
  })

  const options = {
    method: "POST",
    headers: myHeaders,
    body: paymentPayload,
  }

  logger.log("Initiating recurring payment", {
    url: `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/payins/createRecurringPayInRegistrationCIT`,
  })

  const response = await fetch(
    `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/payins/createRecurringPayInRegistrationCIT`,
    options
  )

  if (!response.ok) {
    logger.error("Failed to initiate recurring payment", {
      status: response.status,
      statusText: response.statusText,
    })
    throw new Error("Impossible d'initier le paiement récurrent")
  }

  const result = handleMangopayResponse<MangopayRecurringPayIn>(await response.json())
  logger.log("Recurring payment initiated successfully", { result })

  return result
}

export async function getPayInDetails(payInId: string) {
  const myHeaders = new Headers()
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)

  const options = { method: "GET", headers: myHeaders }

  logger.log("Fetching PayIn details", {
    url: `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/payins/${payInId}`,
  })

  const response = await fetch(`${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/payins/${payInId}`, options)

  if (!response.ok) {
    logger.error("Failed to fetch PayIn details", {
      status: response.status,
      statusText: response.statusText,
    })
    throw new Error("Impossible de récupérer les détails du paiement")
  }

  const result = handleMangopayResponse<MangopayRecurringPayIn>(await response.json())
  logger.log("PayIn details fetched successfully", { result })

  return result
}

export async function getRecurringRegistrationDetails(
  registrationId: string
): Promise<MangopayRecurringPayinRegistration> {
  const myHeaders = new Headers()
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)

  const options = { method: "GET", headers: myHeaders }

  const url = `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/payins/viewRecurringPayment/${registrationId}`

  logger.log("Fetching Recurring Registration details", { url })

  const response = await fetch(url, options)

  if (!response.ok) {
    logger.error("Failed to fetch Recurring Registration details", {
      status: response.status,
      statusText: response.statusText,
      registrationId: registrationId,
    })

    throw new Error("Impossible de récupérer les détails de l'enregistrement de paiement récurrent")
  }

  const result = handleMangopayResponse<MangopayRecurringPayinRegistration>(await response.json())
  logger.log("Recurring Registration details fetched successfully", {
    registrationId: registrationId,
    status: result.Status,
  })

  return result
}

async function updateRecurringPayment(
  registrationId: string,
  payload: Record<string, unknown>,
  action: string
): Promise<MangopayRecurringPayinRegistration> {
  const myHeaders = new Headers()
  myHeaders.append("Content-Type", "application/json")
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)
  myHeaders.append("Idempotency-Key", `${action}_recurring_${registrationId}_${Date.now()}`)

  const updatePayload = JSON.stringify(payload)

  const url = `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/payins/updateRecurringPayin/${registrationId}`

  const options = {
    method: "PUT",
    headers: myHeaders,
    body: updatePayload,
  }

  logger.log(`Attempting to __${action}__ recurring payment registration`, { url, registrationId, payload })

  const response = await fetch(url, options)

  if (!response.ok) {
    const errorBody = await response.text().catch(() => "Could not read error body")
    logger.error(`Failed to __${action}__ recurring payment registration`, {
      status: response.status,
      statusText: response.statusText,
      registrationId: registrationId,
      errorBody: errorBody,
    })

    try {
      const errorJson = JSON.parse(errorBody)
      handleMangopayResponse(errorJson)
    } catch (e) {
      throw new Error(
        `Impossible de ${action} l'enregistrement récurrent ${registrationId}. Status: ${response.status}`
      )
    }
  }

  const result = handleMangopayResponse<MangopayRecurringPayinRegistration>(await response.json())
  logger.log(`Recurring payment registration __${action}__ successfully via API`, {
    registrationId: registrationId,
    newStatus: result.Status,
  })

  return result
}

export async function cancelRecurringPaymentRegistration(
  registrationId: string
): Promise<MangopayRecurringPayinRegistration> {
  const result = await updateRecurringPayment(registrationId, { Status: "ENDED" }, "cancel")

  if (result.Status !== "ENDED") {
    logger.warn(`Registration status after cancel attempt is reported as ${result.Status}, not ENDED.`, {
      registrationId,
    })
  }

  return result
}

export async function updateRecurringPaymentCard(
  registrationId: string,
  newCardId: string
): Promise<MangopayRecurringPayinRegistration> {
  return await updateRecurringPayment(registrationId, { CardId: newCardId }, "update card")
}

export async function getCard(cardId: string): Promise<MangopayCardType> {
  const myHeaders = new Headers()
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)

  const options = { method: "GET", headers: myHeaders }

  logger.log("Fetching card details", {
    url: `${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/cards/${cardId}`,
  })

  const response = await fetch(`${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/cards/${cardId}`, options)

  if (!response.ok) {
    logger.error("Failed to fetch card details", {
      status: response.status,
      statusText: response.statusText,
      cardId: cardId,
    })
    throw new Error(`Impossible de récupérer les détails de la carte ${cardId}. Status: ${response.status}`)
  }

  const result = handleMangopayResponse<MangopayCardType>(await response.json())
  logger.log("Card details fetched successfully", { cardId: cardId })

  return result
}

export async function desactivateCard(cardId: string): Promise<MangopayCardType> {
  const card = await getCard(cardId)
  if (!card.Active) {
    logger.log("Card is already inactive", { cardId })
    return card
  }

  const myHeaders = new Headers()
  myHeaders.append("Content-Type", "application/json")
  myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)

  const desactivatePayload = JSON.stringify({
    Id: cardId,
    Active: false,
  })

  const options = {
    method: "POST",
    headers: myHeaders,
    body: desactivatePayload,
  }

  logger.log("Attempting to deactivate card", { cardId })

  const response = await fetch(`${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/cards/desactivate`, options)

  if (!response.ok) {
    logger.error("Failed to deactivate card", {
      status: response.status,
      statusText: response.statusText,
      cardId: cardId,
    })
    throw new Error(`Impossible de désactiver la carte ${cardId}. Status: ${response.status}`)
  }

  const result = handleMangopayResponse<MangopayCardType>(await response.json())
  logger.log("Card deactivated successfully", { cardId: cardId, active: result.Active })

  return result
}

export const WHITE_LISTED_COUNTRIES = [
  "AD",
  "AE",
  "AG",
  "AI",
  "AL",
  "AM",
  "AO",
  "AQ",
  "AR",
  "AS",
  "AT",
  "AU",
  "AW",
  "AX",
  "AZ",
  "BA",
  "BB",
  "BD",
  "BE",
  "BF",
  "BG",
  "BH",
  "BI",
  "BJ",
  "BL",
  "BM",
  "BN",
  "BO",
  "BQ",
  "BR",
  "BS",
  "BW",
  "BZ",
  "CA",
  "CC",
  "CG",
  "CH",
  "CI",
  "CK",
  "CL",
  "CM",
  "CN",
  "CO",
  "CR",
  "CV",
  "CW",
  "CX",
  "CY",
  "CZ",
  "DE",
  "DJ",
  "DK",
  "DM",
  "DO",
  "DZ",
  "EC",
  "EE",
  "EG",
  "EH",
  "ES",
  "FI",
  "FJ",
  "FK",
  "FM",
  "FO",
  "FR",
  "GA",
  "GB",
  "GE",
  "GF",
  "GG",
  "GH",
  "GI",
  "GL",
  "GM",
  "GP",
  "GT",
  "GU",
  "GW",
  "GY",
  "HK",
  "HN",
  "HR",
  "HT",
  "HU",
  "ID",
  "IE",
  "IL",
  "IM",
  "IN",
  "IO",
  "IS",
  "IT",
  "JE",
  "JM",
  "JO",
  "JP",
  "KE",
  "KG",
  "KH",
  "KI",
  "KM",
  "KN",
  "KR",
  "KW",
  "KY",
  "KZ",
  "LA",
  "LC",
  "LI",
  "LK",
  "LS",
  "LT",
  "LU",
  "LV",
  "MA",
  "MC",
  "MD",
  "MF",
  "MG",
  "MH",
  "MK",
  "MN",
  "MO",
  "MP",
  "MQ",
  "MR",
  "MS",
  "MT",
  "MU",
  "MW",
  "MX",
  "MY",
  "MZ",
  "NA",
  "NC",
  "NE",
  "NF",
  "NG",
  "NI",
  "NL",
  "NO",
  "NP",
  "NR",
  "NU",
  "NZ",
  "OM",
  "PA",
  "PE",
  "PF",
  "PG",
  "PH",
  "PK",
  "PL",
  "PM",
  "PN",
  "PR",
  "PS",
  "PT",
  "PW",
  "PY",
  "QA",
  "RE",
  "RO",
  "RS",
  "RW",
  "SA",
  "SB",
  "SC",
  "SE",
  "SG",
  "SH",
  "SI",
  "SJ",
  "SK",
  "SL",
  "SM",
  "SN",
  "SR",
  "ST",
  "SV",
  "SX",
  "SZ",
  "TC",
  "TD",
  "TF",
  "TG",
  "TH",
  "TK",
  "TL",
  "TM",
  "TN",
  "TO",
  "TR",
  "TT",
  "TV",
  "TW",
  "TZ",
  "UG",
  "UM",
  "US",
  "UZ",
  "VA",
  "VC",
  "VG",
  "VI",
  "VN",
  "VU",
  "WF",
  "WS",
  "XK",
  "YT",
  "ZA",
  "ZM",
]
