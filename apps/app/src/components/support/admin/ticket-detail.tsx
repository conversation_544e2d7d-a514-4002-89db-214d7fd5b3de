"use client"

import { Key, useEffect, useRef, useState } from "react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { ArrowLeft, ChevronDown, Download, Eye, Paperclip, Send } from "lucide-react"
import { toast } from "react-toastify"

import FileUpload from "@/components/ui/file-upload"
import ImageViewer from "@/components/ui/image-viewer"
import { maxUploadSize } from "@/constants"
import { trpc } from "@/lib/trpc/client"
import { getImageUrl } from "@/lib/utils/client-utils"
import { TicketStatus } from "@/types/support"
import { Avatar } from "@nextui-org/avatar"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardFooter, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"
import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from "@nextui-org/dropdown"
import { Textarea } from "@nextui-org/input"
import { Skeleton } from "@nextui-org/skeleton"
import { Tooltip } from "@nextui-org/tooltip"

interface AdminTicketDetailProps {
  ticketId: string
}

const statusLabels = {
  OPEN: "Ouvert",
  IN_PROGRESS: "En cours",
  COMPLETED: "Terminé",
}

export default function AdminTicketDetail({ ticketId }: AdminTicketDetailProps) {
  const router = useRouter()
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const { data: ticket, isLoading, refetch } = trpc.ticket.getById.useQuery(ticketId)

  const [message, setMessage] = useState("")
  const [file, setFile] = useState<File | null>(null)
  const [showAttachment, setShowAttachment] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [imageModalOpen, setImageModalOpen] = useState(false)
  const [selectedImage, setSelectedImage] = useState<{ url: string; filename: string } | null>(null)

  const getPresignedUrlMutation = trpc.upload.presignedUrl.useMutation()
  const sendMessageMutation = trpc.message.create.useMutation()
  const updateStatusMutation = trpc.ticket.updateStatus.useMutation()

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [ticket?.messages])

  useEffect(() => {
    const interval = setInterval(() => {
      refetch()
    }, 5000)

    return () => clearInterval(interval)
  }, [refetch])

  const openImageViewer = (url: string, filename: string) => {
    setSelectedImage({ url, filename })
    setImageModalOpen(true)
  }

  const getStatusColor = (status: TicketStatus): "warning" | "primary" | "success" => {
    switch (status) {
      case TicketStatus.OPEN:
        return "warning"
      case TicketStatus.IN_PROGRESS:
        return "primary"
      case TicketStatus.COMPLETED:
        return "success"
    }
  }

  const handleStatusChange = async (status: TicketStatus) => {
    if (!ticket || ticket.status === status) return

    try {
      await updateStatusMutation.mutateAsync({
        ticketId,
        status,
      })

      refetch()
      toast.success("Statut mis à jour avec succès")
    } catch (error) {
      toast.error("Erreur inconnue")
    }
  }

  const handleDownloadFile = (url: string, filename: string) => {
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const blobUrl = URL.createObjectURL(blob)

        const link = document.createElement("a")
        link.href = blobUrl
        link.download = filename
        document.body.appendChild(link)
        link.click()

        document.body.removeChild(link)
        setTimeout(() => URL.revokeObjectURL(blobUrl), 100)
      })
      .catch((error) => {
        console.error("Erreur lors du téléchargement:", error)
        toast.error("Échec du téléchargement du fichier")
      })
  }

  const handleSendMessage = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!message.trim() && !file) return

    setIsSubmitting(true)

    try {
      let attachmentId: string | undefined = undefined

      if (file) {
        if (file.size > maxUploadSize) {
          toast.error("Fichier trop volumineux")
          setIsSubmitting(false)
          return
        }

        const { url, fields } = await getPresignedUrlMutation.mutateAsync({
          filename: file.name,
          filetype: file.type,
        })

        const formData = new FormData()
        Object.entries(fields).forEach(([key, value]) => {
          formData.append(key, value as string)
        })
        formData.append("file", file)

        const uploadResponse = await fetch(url, {
          method: "POST",
          body: formData,
        })

        if (uploadResponse.ok) {
          // Utiliser la clé du fichier comme attachmentId
          attachmentId = fields.key
        } else {
          throw new Error("Upload failed")
        }
      }

      // Créer le message avec l'attachmentId
      await sendMessageMutation.mutateAsync({
        ticketId,
        content: message.trim() || " ",
        attachmentId,
      })

      setMessage("")
      setFile(null)
      setShowAttachment(false)
      refetch()
    } catch (error) {
      console.error("Error sending message:", error)
      toast.error("Échec de l'envoi")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="mx-auto flex w-full max-w-4xl flex-col gap-4">
        <Button
          variant="light"
          startContent={<ArrowLeft size={20} />}
          onPress={() => router.push("/admin/support")}
          className="mb-2 w-fit"
        >
          Retour à la liste
        </Button>

        <Card className="w-full">
          <CardHeader>
            <Skeleton className="h-8 w-3/4 rounded-lg" />
          </CardHeader>
          <CardBody>
            <Skeleton className="h-32 w-full rounded-lg" />
          </CardBody>
        </Card>
      </div>
    )
  }

  if (!ticket) return null

  return (
    <div className="mx-auto flex w-full max-w-4xl flex-col gap-4">
      <Button
        variant="light"
        startContent={<ArrowLeft size={20} />}
        onPress={() => router.push("/admin/support")}
        className="mb-2 w-fit"
      >
        Retour à la liste
      </Button>

      <Card className="w-full">
        <CardHeader className="flex flex-col items-start justify-between gap-2 sm:flex-row sm:items-center">
          <div className="flex-1">
            <h3 className="text-xl font-semibold">{ticket.title}</h3>
            <div className="mt-1 text-sm text-default-500">
              Créé le: {format(new Date(ticket.createdAt), "dd/MM/yyyy HH:mm")}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Dropdown>
              <DropdownTrigger>
                <Button
                  variant="flat"
                  color={getStatusColor(ticket.status as TicketStatus)}
                  endContent={<ChevronDown size={16} />}
                  size="sm"
                >
                  {statusLabels[ticket.status]}
                </Button>
              </DropdownTrigger>
              <DropdownMenu
                aria-label="Status options"
                onAction={(key: Key) => handleStatusChange(key as TicketStatus)}
                disallowEmptySelection
                selectionMode="single"
                selectedKeys={[ticket.status]}
              >
                <DropdownItem key={TicketStatus.OPEN}>Ouvert</DropdownItem>
                <DropdownItem key={TicketStatus.IN_PROGRESS}>En cours</DropdownItem>
                <DropdownItem key={TicketStatus.COMPLETED}>Terminé</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
        </CardHeader>

        <Divider />

        <CardBody className="px-4 py-2">
          <div className="mb-4 flex max-h-[500px] flex-col gap-4 overflow-y-auto rounded-lg bg-default-50 p-3">
            {ticket.messages.map((msg) => {
              const isAdmin = msg.sender.role === "ADMIN"
              const filename = msg.attachment?.key.split("/").pop() || "fichier"

              return (
                <div key={msg.id} className={`flex ${isAdmin ? "justify-end" : "justify-start"}`}>
                  <div className={`flex gap-2 ${isAdmin ? "flex-row-reverse" : "flex-row"} max-w-[80%]`}>
                    <Avatar
                      className="size-8 min-w-8 shrink-0"
                      src={msg.sender.profilePicture ? getImageUrl(msg.sender.profilePicture)! : undefined}
                      name={msg.sender.name || undefined}
                      showFallback
                    />
                    <div>
                      <div
                        className={`rounded-lg px-4 py-2 shadow-sm ${isAdmin ? "bg-primary text-primary-foreground" : "bg-default-100"
                          }`}
                      >
                        <div className="mb-1 flex items-center gap-1 text-xs font-medium">
                          {msg.sender.name || "User"}
                        </div>
                        <p className="whitespace-pre-wrap break-all">{msg.content}</p>
                        {msg.attachment && (
                          <div className="mt-2">
                            {msg.attachment.filetype.startsWith("image/") ? (
                              <div className="relative">
                                <div className="group relative">
                                  <Image
                                    width={300}
                                    height={300}
                                    src={getImageUrl(msg.attachment)!}
                                    alt="Attachment"
                                    className="max-h-64 w-auto max-w-full rounded object-contain transition-all hover:brightness-90"
                                  />
                                  <div className="absolute inset-0 flex items-center justify-center gap-2 opacity-0 transition-opacity group-hover:opacity-100">
                                    <Button
                                      isIconOnly
                                      size="sm"
                                      variant="flat"
                                      className="bg-black/30 text-white"
                                      onPress={() => openImageViewer(getImageUrl(msg.attachment)!, filename)}
                                    >
                                      <Eye size={16} />
                                    </Button>
                                    <Button
                                      isIconOnly
                                      size="sm"
                                      variant="flat"
                                      className="bg-black/30 text-white"
                                      onPress={() => handleDownloadFile(getImageUrl(msg.attachment)!, filename)}
                                    >
                                      <Download size={16} />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <div className="flex items-center gap-2 rounded bg-default-200/50 p-2">
                                <Paperclip size={16} />
                                <span className="flex-1 truncate text-sm">{filename}</span>
                                <Button
                                  isIconOnly
                                  size="sm"
                                  variant="flat"
                                  onPress={() => handleDownloadFile(getImageUrl(msg.attachment)!, filename)}
                                >
                                  <Download size={14} />
                                </Button>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      <div className={`mt-1 text-xs text-default-500 ${isAdmin ? "text-right" : "text-left"}`}>
                        {format(new Date(msg.createdAt), "HH:mm")}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
            <div ref={messagesEndRef} />
          </div>

          {ticket.status !== TicketStatus.COMPLETED && (
            <form onSubmit={handleSendMessage} className="mt-4">
              {showAttachment && (
                <div className="mb-4">
                  <FileUpload
                    dictionary={{
                      uploadDescription: "Uploadez un fichier",
                      invalidFileType: "Type de fichier invalide",
                      cropImage: "Recadrer l'image",
                      cancel: "Annuler",
                      reset: "Réinitialiser",
                      save: "Sauvegarder",
                      loading: "Chargement",
                    }}
                    onFilesChange={(files) => setFile(files[0])}
                    maxFiles={1}
                    accept={{
                      "image/png": [".png"],
                      "image/jpeg": [".jpg", ".jpeg"],
                      // "application/pdf": [".pdf"],
                    }}
                    disabled={isSubmitting}
                  />
                </div>
              )}

              <div className="flex gap-2">
                <Tooltip content="Joindre un fichier">
                  <Button isIconOnly variant="flat" onPress={() => setShowAttachment(!showAttachment)}>
                    <Paperclip size={20} />
                  </Button>
                </Tooltip>

                <Textarea
                  placeholder="Écrivez un message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  minRows={1}
                  maxRows={4}
                  disabled={isSubmitting}
                  classNames={{
                    input: "resize-none",
                    inputWrapper: "border-2 focus-within:border-primary/50",
                  }}
                />

                <Button
                  isIconOnly
                  color="primary"
                  type="submit"
                  isDisabled={isSubmitting || (!message.trim() && !file)}
                  isLoading={isSubmitting}
                >
                  <Send size={20} />
                </Button>
              </div>
            </form>
          )}
        </CardBody>

        {ticket.status !== TicketStatus.COMPLETED && (
          <CardFooter className="flex justify-end border-t border-default-200">
            <Button
              color="success"
              variant="flat"
              onPress={() => handleStatusChange(TicketStatus.COMPLETED)}
              startContent={<ChevronDown size={16} />}
            >
              Clôturer ce ticket
            </Button>
          </CardFooter>
        )}
      </Card>

      {/* Modal pour visualiser les images en plein écran */}
      {selectedImage && (
        <ImageViewer
          isOpen={imageModalOpen}
          onClose={() => setImageModalOpen(false)}
          imageUrl={selectedImage.url}
          filename={selectedImage.filename}
        />
      )}
    </div>
  )
}
