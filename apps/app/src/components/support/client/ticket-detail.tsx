"use client"

import { useEffect, useRef, useState } from "react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { ArrowLeft, Download, Eye, Paperclip, Send } from "lucide-react"
import { toast } from "react-toastify"

import FileUpload from "@/components/ui/file-upload"
import ImageViewer from "@/components/ui/image-viewer"
import { maxUploadSize } from "@/constants"
import { trpc } from "@/lib/trpc/client"
import { getImageUrl } from "@/lib/utils/client-utils"
import { TicketStatus } from "@/types/support"
import { Avatar } from "@nextui-org/avatar"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Divider } from "@nextui-org/divider"
import { Textarea } from "@nextui-org/input"
import { Skeleton } from "@nextui-org/skeleton"
import { Tooltip } from "@nextui-org/tooltip"

interface TicketDetailProps {
  ticketId: string
}

export default function TicketDetail({ ticketId }: TicketDetailProps) {
  const router = useRouter()
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const { data: ticket, isLoading, refetch } = trpc.ticket.getById.useQuery(ticketId)

  const [message, setMessage] = useState("")
  const [file, setFile] = useState<File | null>(null)
  const [showAttachment, setShowAttachment] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [imageModalOpen, setImageModalOpen] = useState(false)
  const [selectedImage, setSelectedImage] = useState<{ url: string; filename: string } | null>(null)

  const getPresignedUrlMutation = trpc.upload.presignedUrl.useMutation()
  const sendMessageMutation = trpc.message.create.useMutation()

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [ticket?.messages])

  useEffect(() => {
    const interval = setInterval(() => {
      refetch()
    }, 5000)

    return () => clearInterval(interval)
  }, [refetch])

  const openImageViewer = (url: string, filename: string) => {
    setSelectedImage({ url, filename })
    setImageModalOpen(true)
  }

  const getStatusColor = (status: string): "warning" | "primary" | "success" => {
    switch (status) {
      case "OPEN":
        return "warning"
      case "IN_PROGRESS":
        return "primary"
      case "COMPLETED":
        return "success"
      default:
        return "warning"
    }
  }

  const statusLabels = {
    OPEN: "Ouvert",
    IN_PROGRESS: "En cours",
    COMPLETED: "Terminé",
  }

  const handleDownloadFile = (url: string, filename: string) => {
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const blobUrl = URL.createObjectURL(blob)

        const link = document.createElement("a")
        link.href = blobUrl
        link.download = filename
        document.body.appendChild(link)
        link.click()

        document.body.removeChild(link)
        setTimeout(() => URL.revokeObjectURL(blobUrl), 100)
      })
      .catch((error) => {
        console.error("Erreur lors du téléchargement:", error)
        toast.error("Échec du téléchargement du fichier")
      })
  }

  const handleSendMessage = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!message.trim() && !file) return

    setIsSubmitting(true)

    try {
      let attachmentId: string | undefined = undefined

      if (file) {
        if (file.size > maxUploadSize) {
          toast.error("Le fichier est trop volumineux!")
          setIsSubmitting(false)
          return
        }

        const { url, fields } = await getPresignedUrlMutation.mutateAsync({
          filename: file.name,
          filetype: file.type,
        })

        const formData = new FormData()
        Object.entries(fields).forEach(([key, value]) => {
          formData.append(key, value as string)
        })
        formData.append("file", file)

        const uploadResponse = await fetch(url, {
          method: "POST",
          body: formData,
        })

        if (uploadResponse.ok) {
          // Utiliser la clé du fichier comme attachmentId
          attachmentId = fields.key
        } else {
          throw new Error("Upload failed")
        }
      }

      // Créer le message avec l'attachmentId
      await sendMessageMutation.mutateAsync({
        ticketId,
        content: message.trim() || " ", // Espace si seulement pièce jointe
        attachmentId,
      })

      setMessage("")
      setFile(null)
      setShowAttachment(false)
      refetch()
    } catch (error) {
      console.error("Error sending message:", error)
      toast.error("Échec de l'envoi du message!")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="mx-auto flex w-full flex-col gap-4">
        <Button
          variant="light"
          startContent={<ArrowLeft size={20} />}
          onPress={() => router.push("/support")}
          className="mb-2 w-fit"
        >
          Retour à la liste
        </Button>

        <Card className="w-full">
          <CardHeader className="flex gap-3">
            <Skeleton className="h-8 w-3/4 rounded-lg" />
          </CardHeader>
          <CardBody>
            <Skeleton className="h-32 w-full rounded-lg" />
          </CardBody>
        </Card>
      </div>
    )
  }

  if (!ticket) return null

  return (
    <div className="mx-auto flex w-full flex-col gap-4">
      <Button
        variant="light"
        startContent={<ArrowLeft size={20} />}
        onPress={() => router.push("/support")}
        className="mb-2 w-fit"
      >
        Retour à la liste
      </Button>

      <Card className="w-full">
        <CardHeader className="flex items-center justify-between">
          <h3 className="text-xl font-semibold">{ticket.title}</h3>
          <Chip color={getStatusColor(ticket.status as TicketStatus)} variant="flat">
            {statusLabels[ticket.status]}
          </Chip>
        </CardHeader>

        <Divider />

        <CardBody className="px-4 py-2">
          <div className="mb-4 text-sm text-default-500">
            Créé le: {format(new Date(ticket.createdAt), "dd/MM/yyyy HH:mm")}
          </div>

          <div className="mb-4 flex max-h-[500px] flex-col gap-4 overflow-y-auto overflow-x-hidden rounded-lg bg-default-50 p-3">
            {ticket.messages.map((msg) => {
              const isCurrentUser = msg.sender.role === "USER"
              const filename = msg.attachment?.key.split("/").pop() || "fichier"

              return (
                <div key={msg.id} className={`flex max-w-full ${isCurrentUser ? "justify-end" : "justify-start"}`}>
                  <div className={`flex gap-2 ${isCurrentUser ? "flex-row-reverse" : "flex-row"} max-w-[80%]`}>
                    <Avatar
                      className="size-8 min-w-8 shrink-0"
                      src={
                        isCurrentUser
                          ? msg.sender.profilePicture
                            ? getImageUrl(msg.sender.profilePicture)!
                            : undefined
                          : "/logo.svg"
                      }
                      name={isCurrentUser ? msg.sender.name || undefined : "Cohead Support"}
                      showFallback
                    />
                    <div>
                      <div
                        className={`rounded-lg px-4 py-2 shadow-sm ${isCurrentUser ? "bg-primary text-primary-foreground" : "bg-default-100"
                          }`}
                      >
                        <p className="whitespace-pre-wrap break-all">{msg.content}</p>
                        {msg.attachment && (
                          <div className="mt-2">
                            {msg.attachment.filetype.startsWith("image/") ? (
                              <div className="relative">
                                <div className="group relative">
                                  <Image
                                    width={300}
                                    height={300}
                                    src={getImageUrl(msg.attachment)!}
                                    alt="Attachment"
                                    className="max-h-64 w-auto max-w-full rounded object-contain transition-all hover:brightness-90"
                                  />
                                  <div className="absolute inset-0 flex items-center justify-center gap-2 opacity-0 transition-opacity group-hover:opacity-100">
                                    <Button
                                      isIconOnly
                                      size="sm"
                                      variant="flat"
                                      className="bg-black/30 text-white"
                                      onPress={() => openImageViewer(getImageUrl(msg.attachment)!, filename)}
                                    >
                                      <Eye size={16} />
                                    </Button>
                                    <Button
                                      isIconOnly
                                      size="sm"
                                      variant="flat"
                                      className="bg-black/30 text-white"
                                      onPress={() => handleDownloadFile(getImageUrl(msg.attachment)!, filename)}
                                    >
                                      <Download size={16} />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <div className="flex items-center gap-2 rounded bg-default-200/50 p-2">
                                <Paperclip size={16} />
                                <span className="flex-1 truncate text-sm">{filename}</span>
                                <Button
                                  isIconOnly
                                  size="sm"
                                  variant="flat"
                                  onPress={() => handleDownloadFile(getImageUrl(msg.attachment)!, filename)}
                                >
                                  <Download size={14} />
                                </Button>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      <div className={`mt-1 text-xs text-default-500 ${isCurrentUser ? "text-right" : "text-left"}`}>
                        {format(new Date(msg.createdAt), "HH:mm")}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
            <div ref={messagesEndRef} />
          </div>

          {ticket.status !== "COMPLETED" && (
            <form onSubmit={handleSendMessage} className="mt-4">
              {showAttachment && (
                <div className="mb-4">
                  <FileUpload
                    dictionary={{
                      uploadDescription: "Uploadez un fichier",
                      invalidFileType: "Type de fichier invalide",
                      cropImage: "Recadrer l'image",
                      cancel: "Annuler",
                      reset: "Réinitialiser",
                      save: "Sauvegarder",
                      loading: "Chargement",
                    }}
                    onFilesChange={(files) => setFile(files[0])}
                    maxFiles={1}
                    accept={{
                      "image/png": [".png"],
                      "image/jpeg": [".jpg", ".jpeg"],
                      // "application/pdf": [".pdf"],
                    }}
                    disabled={isSubmitting}
                  />
                </div>
              )}

              <div className="flex gap-2">
                <Tooltip content="Joindre un fichier">
                  <Button isIconOnly variant="flat" onPress={() => setShowAttachment(!showAttachment)}>
                    <Paperclip size={20} />
                  </Button>
                </Tooltip>

                <Textarea
                  placeholder="Écrivez un message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  minRows={1}
                  maxRows={4}
                  disabled={isSubmitting}
                  classNames={{
                    input: "resize-none",
                    inputWrapper: "border-2 focus-within:border-primary/50",
                  }}
                />

                <Button
                  isIconOnly
                  color="primary"
                  type="submit"
                  isDisabled={isSubmitting || (!message.trim() && !file)}
                  isLoading={isSubmitting}
                >
                  <Send size={20} />
                </Button>
              </div>
            </form>
          )}
        </CardBody>
      </Card>

      {/* Modal pour visualiser les images en plein écran */}
      {selectedImage && (
        <ImageViewer
          isOpen={imageModalOpen}
          onClose={() => setImageModalOpen(false)}
          imageUrl={selectedImage.url}
          filename={selectedImage.filename}
        />
      )}
    </div>
  )
}
